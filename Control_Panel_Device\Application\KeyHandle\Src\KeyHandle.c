//----------------------------------------------------------------------------
/**
* @remark Confidential property of TRIED.
* @remark Copyright (c) 2025 TRIED.All Rights Reserved.
*
* <AUTHOR>
* @remark Requirement IDs
* @remark Reusability status
* @file KeyHandle.c
*
* @brief Key handling module implementation file
*
*/
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "KeyHandle.h"

//----------------------------------------------------------------------------
// Private Definitions:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Private Function Prototypes:
//----------------------------------------------------------------------------
static KEYHANDLE_STATUS_E KeyHandle_eValidateConfig(const KEYHANDLE_CONFIG_T* const psConfig);
static KEYHANDLE_STATUS_E KeyHandle_eInitDefaultKeyMaps(KEYHANDLE_CONFIG_T* const psConfig);
static KEYHANDLE_STATUS_E KeyHandle_eInitDefaultComboKeyMaps(KEYHANDLE_CONFIG_T* const psConfig);
static KEYHANDLE_STATUS_E KeyHandle_eDebounceKeys(KEYHANDLE_HANDLE_T* const psHandle);
static KEYHANDLE_STATUS_E KeyHandle_eDetectKeyEvents(KEYHANDLE_HANDLE_T* const psHandle);
static KEYHANDLE_STATUS_E KeyHandle_eDetectComboEvents(KEYHANDLE_HANDLE_T* const psHandle);
static KEYHANDLE_SINGLE_KEY_E KeyHandle_eMatrixToSingleKey(const KEYHANDLE_HANDLE_T* const psHandle, U8 u8MatrixKey);
static BOOL KeyHandle_bIsComboMatch(const KEYHANDLE_COMBO_KEY_MAP_T* const psCombo, const KEYHANDLE_SINGLE_KEY_E* const aeActiveKeys, U8 u8KeyCount);
static void KeyHandle_vTriggerEvent(const KEYHANDLE_HANDLE_T* const psHandle, const KEYHANDLE_EVENT_T* const psEvent);
static U32 KeyHandle_u32GetTickMs(void);
static U8 KeyHandle_u8GetCurrentKeyCount(const KEYHANDLE_HANDLE_T* const psHandle);
static void KeyHandle_vResetSingleKeyStates(KEYHANDLE_HANDLE_T* const psHandle);

/* State machine handler functions */
static KEYHANDLE_STATE_MACHINE_E KeyHandle_eStateIdle(KEYHANDLE_HANDLE_T* const psHandle);
static KEYHANDLE_STATE_MACHINE_E KeyHandle_eStateScan(KEYHANDLE_HANDLE_T* const psHandle);
static KEYHANDLE_STATE_MACHINE_E KeyHandle_eStateDebounce(KEYHANDLE_HANDLE_T* const psHandle);
static KEYHANDLE_STATE_MACHINE_E KeyHandle_eStateDetectSingle(KEYHANDLE_HANDLE_T* const psHandle);
static KEYHANDLE_STATE_MACHINE_E KeyHandle_eStateDetectCombo(KEYHANDLE_HANDLE_T* const psHandle);
static void KeyHandle_vResetState(KEYHANDLE_HANDLE_T* const psHandle);

//----------------------------------------------------------------------------
// Private Data:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Public Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Initialize key handling module.
* @remark None.
*
* @param psHandle [in]: Key handling handle pointer.
* @param psConfig [in]: Configuration parameter pointer.
* @return Initialization status.
*/
KEYHANDLE_STATUS_E KeyHandle_eInit(KEYHANDLE_HANDLE_T* const psHandle, const KEYHANDLE_CONFIG_T* const psConfig)
{
    /* Parameter validity check */
    if (psHandle == NULL_D)
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Validate entire configuration */
    if (KeyHandle_eValidateConfig(psConfig) != KEYHANDLE_OK_E)
    {
        return KEYHANDLE_ERROR_E;
    }
    
    HALKEY_CONFIG_T sHalConfig;
    /* Get HalKey default configuration */
    if (HalKey_eGetDefaultConfig(&sHalConfig) != HALKEY_OK_E)
    {
        return KEYHANDLE_ERROR_E;
    }

    /* Initialize HalKey */
    if (HalKey_eInit(&psHandle->sHalKey, &sHalConfig) != HALKEY_OK_E)
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Save configuration parameters */
    psHandle->sConfig = *psConfig;
    
    /* Initialize state information */
    psHandle->sState = (KEYHANDLE_STATE_T){0U};
    /* Set state machine to idle state */
    psHandle->sState.eStateMachine = KEYHANDLE_STATE_IDLE_E;
    /* Initialize key processing mode flags */
    psHandle->sState.bInComboMode = FALSE_D;
    psHandle->sState.u8CurrentKeyCount = 0U;
    
    /* Set initialization complete flag */
    psHandle->bInitialized = TRUE_D;

    return KEYHANDLE_OK_E;
}

/**
* @brief Deinitialize key handling module.
* @remark None.
*
* @param psHandle [in]: Key handling handle pointer.
* @return Deinitialization status.
*/
KEYHANDLE_STATUS_E KeyHandle_eDeInit(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if (psHandle == NULL_D)
    {
        return KEYHANDLE_ERROR_E;
    }

    /* Check if key handling module is initialized */
    if (psHandle->bInitialized)
    {
        /* Deinitialize HAL layer key module */
        HalKey_eDeInit(&psHandle->sHalKey);

        /* Clear handle */
        psHandle->sState = (KEYHANDLE_STATE_T){0U};
        psHandle->bInitialized = FALSE_D;
    }
    
    return KEYHANDLE_OK_E;
}

/**
* @brief Get default configuration.
* @remark None.
*
* @param psConfig [out]: Configuration structure pointer, used to return default configuration.
* @return Operation status.
*/
KEYHANDLE_STATUS_E KeyHandle_eGetDefaultConfig(KEYHANDLE_CONFIG_T* const psConfig)
{
    /* Parameter validity check */
    if (psConfig == NULL_D)
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Set default time parameters */
    psConfig->u32DebounceTimeMs = KEYHANDLE_DEFAULT_DEBOUNCE_TIME_MS_D;
    psConfig->u32LongPressTimeMs = KEYHANDLE_DEFAULT_LONG_PRESS_TIME_MS_D;
    psConfig->u32LongPressRepeatMs = KEYHANDLE_DEFAULT_LONG_PRESS_REPEAT_MS_D;
    psConfig->u32ComboTimeoutMs = KEYHANDLE_DEFAULT_COMBO_TIMEOUT_MS_D;
    
    /* Set default function enable states */
    psConfig->bEnableShortPress = TRUE_D;
    psConfig->bEnableLongPress = TRUE_D;
    psConfig->bEnableComboKeys = TRUE_D;
    
    /* Initialize callback function arrays to null */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        psConfig->asSingleKeyShortPressCallbacks[i] = NULL_D;
        psConfig->asSingleKeyLongPressCallbacks[i] = NULL_D;
    }
    
    for (U8 i = 0U; i < KEYHANDLE_COMBO_MAX_E; i++)
    {
        psConfig->asComboKeyShortPressCallbacks[i] = NULL_D;
    }
    
    /* Initialize default key mapping configuration */
    if (KeyHandle_eInitDefaultKeyMaps(psConfig) != KEYHANDLE_OK_E)
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Initialize default combination key mapping */
    if (KeyHandle_eInitDefaultComboKeyMaps(psConfig) != KEYHANDLE_OK_E)
    {
        return KEYHANDLE_ERROR_E;
    }
    
    return KEYHANDLE_OK_E;
}

/**
* @brief Process key scanning and event detection using state machine.
* @remark Main processing function implemented as state machine.
*
* @param psHandle [in]: Key handling handle pointer.
* @return Processing status.
*/
KEYHANDLE_STATUS_E KeyHandle_eProcess(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    KEYHANDLE_STATE_MACHINE_E eNextState = psHandle->sState.eStateMachine;
    
    /* Execute state machine */
    switch (psHandle->sState.eStateMachine)
    {
        case KEYHANDLE_STATE_IDLE_E:
            eNextState = KeyHandle_eStateIdle(psHandle);
            break;
            
        case KEYHANDLE_STATE_SCAN_E:
            eNextState = KeyHandle_eStateScan(psHandle);
            break;
            
        case KEYHANDLE_STATE_DEBOUNCE_E:
            eNextState = KeyHandle_eStateDebounce(psHandle);
            break;
            
        case KEYHANDLE_STATE_DETECT_SINGLE_E:
            eNextState = KeyHandle_eStateDetectSingle(psHandle);
            break;
            
        case KEYHANDLE_STATE_DETECT_COMBO_E:
            eNextState = KeyHandle_eStateDetectCombo(psHandle);
            break;
            
        default:
            /* Invalid state - reset state and return to idle */
            KeyHandle_vResetState(psHandle);
            eNextState = KEYHANDLE_STATE_IDLE_E;
            break;
    }
    
    /* Update state machine state */
    psHandle->sState.eStateMachine = eNextState;
    
    return KEYHANDLE_OK_E;
}

/**
* @brief Set single key short press callback function.
* @remark Set callback function for specific single key short press event.
*
* @param psHandle [in]: Key handling handle pointer.
* @param eSingleKey [in]: Single key type.
* @param pfCallback [in]: Event callback function pointer.
* @return Operation status.
*/
KEYHANDLE_STATUS_E KeyHandle_eSetSingleKeyShortPressCallback(KEYHANDLE_HANDLE_T* const psHandle, KEYHANDLE_SINGLE_KEY_E eSingleKey, KeyHandle_EventCallback_t pfCallback)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (eSingleKey >= KEYHANDLE_SINGLE_KEY_MAX_E))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Set single key short press callback function */
    psHandle->sConfig.asSingleKeyShortPressCallbacks[eSingleKey] = pfCallback;

    return KEYHANDLE_OK_E;
}

/**
* @brief Set single key long press callback function.
* @remark Set callback function for specific single key long press event.
*
* @param psHandle [in]: Key handling handle pointer.
* @param eSingleKey [in]: Single key type.
* @param pfCallback [in]: Event callback function pointer.
* @return Operation status.
*/
KEYHANDLE_STATUS_E KeyHandle_eSetSingleKeyLongPressCallback(KEYHANDLE_HANDLE_T* const psHandle, KEYHANDLE_SINGLE_KEY_E eSingleKey, KeyHandle_EventCallback_t pfCallback)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (eSingleKey >= KEYHANDLE_SINGLE_KEY_MAX_E))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Set single key long press callback function */
    psHandle->sConfig.asSingleKeyLongPressCallbacks[eSingleKey] = pfCallback;
    
    return KEYHANDLE_OK_E;
}

/**
* @brief Set combination key short press callback function.
* @remark Set callback function for specific combination key short press event.
*
* @param psHandle [in]: Key handling handle pointer.
* @param eComboKey [in]: Combination key type.
* @param pfCallback [in]: Event callback function pointer.
* @return Operation status.
*/
KEYHANDLE_STATUS_E KeyHandle_eSetComboKeyShortPressCallback(KEYHANDLE_HANDLE_T* const psHandle, KEYHANDLE_COMBO_KEY_E eComboKey, KeyHandle_EventCallback_t pfCallback)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (eComboKey >= KEYHANDLE_COMBO_MAX_E))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Set combination key short press callback function */
    psHandle->sConfig.asComboKeyShortPressCallbacks[eComboKey] = pfCallback;

    return KEYHANDLE_OK_E;
}

/**
* @brief Enable/disable single key.
* @remark Disable by setting eSingleKey to KEYHANDLE_SINGLE_KEY_MAX_E, enable by restoring original key type.
*        Note: This function now has limited functionality due to removal of bEnabled field.
*        It can only disable keys, not re-enable them without knowing the original key type.
*
* @param psHandle [in]: Key handling handle pointer.
* @param eSingleKey [in]: Target single key type.
* @param bEnable [in]: Enable flag (TRUE_D-enable, FALSE_D-disable).
* @return Operation status.
*/
KEYHANDLE_STATUS_E KeyHandle_eEnableSingleKey(KEYHANDLE_HANDLE_T* const psHandle, KEYHANDLE_SINGLE_KEY_E eSingleKey, BOOL bEnable)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (eSingleKey >= KEYHANDLE_SINGLE_KEY_MAX_E))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Search in mapping table */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        /* Check if key matches */
        if (psHandle->sConfig.asSingleKeyMaps[i].eSingleKey == eSingleKey)
        {
            if (!bEnable)
            {
                /* Disable by setting to invalid key type */
                psHandle->sConfig.asSingleKeyMaps[i].eSingleKey = KEYHANDLE_SINGLE_KEY_MAX_E;
            }
            /* Note: Cannot re-enable without knowing original key type */

            return KEYHANDLE_OK_E;
        }
    }

    return KEYHANDLE_ERROR_E;
}

/**
* @brief Enable/disable combination key.
* @remark Disable by setting u8KeyCount to 0, enable by restoring original key count.
*        Note: This function now has limited functionality due to removal of bEnabled field.
*        It can only disable combo keys, not re-enable them without knowing the original key count.
*
* @param psHandle [in]: Key handling handle pointer.
* @param eComboKey [in]: Target combination key type.
* @param bEnable [in]: Enable flag (TRUE_D-enable, FALSE_D-disable).
* @return Operation status.
*/
KEYHANDLE_STATUS_E KeyHandle_eEnableComboKey(KEYHANDLE_HANDLE_T* const psHandle, KEYHANDLE_COMBO_KEY_E eComboKey, BOOL bEnable)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (eComboKey >= KEYHANDLE_COMBO_MAX_E))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Search in combination key mapping table */
    for (U8 i = 0U; i < KEYHANDLE_COMBO_MAX_E; i++)
    {
        /* Check if combination key matches */
        if (psHandle->sConfig.asComboKeyMaps[i].eComboKey == eComboKey)
        {
            if (!bEnable)
            {
                /* Disable by setting key count to 0 */
                psHandle->sConfig.asComboKeyMaps[i].u8KeyCount = 0U;
            }
            /* Note: Cannot re-enable without knowing original key count */

            return KEYHANDLE_OK_E;
        }
    }

    return KEYHANDLE_ERROR_E;
}

/**
* @brief Remap single key.
* @remark None.
*
* @param psHandle [in]: Key handling handle pointer.
* @param eSingleKey [in]: Target single key type.
* @param u8MatrixKey [in]: New matrix key index (0-15).
* @return Operation status.
*/
KEYHANDLE_STATUS_E KeyHandle_eRemapSingleKey(KEYHANDLE_HANDLE_T* const psHandle, KEYHANDLE_SINGLE_KEY_E eSingleKey, U8 u8MatrixKey)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (eSingleKey >= KEYHANDLE_SINGLE_KEY_MAX_E) || (u8MatrixKey >= KEYHANDLE_SINGLE_KEY_MAX_E))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Search in mapping table and modify matrix key mapping */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        /* Check if key is valid */
        if (psHandle->sConfig.asSingleKeyMaps[i].eSingleKey == eSingleKey)
        {
            psHandle->sConfig.asSingleKeyMaps[i].u8MatrixKey = u8MatrixKey;

            return KEYHANDLE_OK_E;
        }
    }

    return KEYHANDLE_ERROR_E;
}

//----------------------------------------------------------------------------
// Private Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Validate configuration.
* @remark Check if configuration is valid.
*
* @param psConfig [in]: Configuration structure pointer.
* @return Operation status.
*/
KEYHANDLE_STATUS_E KeyHandle_eValidateConfig(const KEYHANDLE_CONFIG_T* const psConfig)
{
    /* Parameter validity check */
    if (psConfig == NULL_D)
    {
        return KEYHANDLE_ERROR_E;
    }

    /* Check basic parameters */
    if ((psConfig->u32DebounceTimeMs == 0U) || (psConfig->u32LongPressTimeMs == 0U) || 
        (psConfig->u32LongPressRepeatMs == 0U) || (psConfig->u32ComboTimeoutMs == 0U) ||
        (psConfig->u32LongPressTimeMs <= psConfig->u32DebounceTimeMs))
    {
        return KEYHANDLE_ERROR_E;
    }

    /* Check single key mapping */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        if (psConfig->asSingleKeyMaps[i].eSingleKey != KEYHANDLE_SINGLE_KEY_MAX_E)
        {
            if (psConfig->asSingleKeyMaps[i].eSingleKey >= KEYHANDLE_SINGLE_KEY_MAX_E)
            {
                return KEYHANDLE_ERROR_E;
            }

            if (psConfig->asSingleKeyMaps[i].u8MatrixKey >= KEYHANDLE_SINGLE_KEY_MAX_E)
            {
                return KEYHANDLE_ERROR_E;
            }

            for (U8 j = i + 1U; j < KEYHANDLE_SINGLE_KEY_MAX_E; j++)
            {
                if ((psConfig->asSingleKeyMaps[j].eSingleKey != KEYHANDLE_SINGLE_KEY_MAX_E) && (psConfig->asSingleKeyMaps[i].u8MatrixKey == psConfig->asSingleKeyMaps[j].u8MatrixKey))
                {
                    return KEYHANDLE_ERROR_E;
                }
            }
        }
    }

    /* Check combination keys */
    if (psConfig->bEnableComboKeys)
    {
        for (U8 i = 0U; i < KEYHANDLE_COMBO_MAX_E; i++)
        {
            if (psConfig->asComboKeyMaps[i].u8KeyCount > 0U)
            {
                if (psConfig->asComboKeyMaps[i].eComboKey >= KEYHANDLE_COMBO_MAX_E)
                {
                    return KEYHANDLE_ERROR_E;
                }
                if ((psConfig->asComboKeyMaps[i].u8KeyCount < 2U) || (psConfig->asComboKeyMaps[i].u8KeyCount > KEYHANDLE_MAX_COMBO_SIZE_D))
                {
                    return KEYHANDLE_ERROR_E;
                }
                for (U8 j = 0U; j < psConfig->asComboKeyMaps[i].u8KeyCount; j++)
                {
                    if (psConfig->asComboKeyMaps[i].aeKeys[j] >= KEYHANDLE_SINGLE_KEY_MAX_E)
                    {
                        return KEYHANDLE_ERROR_E;
                    }
                    for (U8 k = j + 1U; k < psConfig->asComboKeyMaps[i].u8KeyCount; k++)
                    {
                        if (psConfig->asComboKeyMaps[i].aeKeys[j] == psConfig->asComboKeyMaps[i].aeKeys[k])
                        {
                            return KEYHANDLE_ERROR_E;
                        }
                    }
                }
                for (U8 j = 0U; j < psConfig->asComboKeyMaps[i].u8KeyCount; j++)
                {
                    BOOL bFound = FALSE_D;
                    for (U8 k = 0U; k < KEYHANDLE_SINGLE_KEY_MAX_E; k++)
                    {
                        if ((psConfig->asSingleKeyMaps[k].eSingleKey != KEYHANDLE_SINGLE_KEY_MAX_E) && (psConfig->asSingleKeyMaps[k].eSingleKey == psConfig->asComboKeyMaps[i].aeKeys[j]))
                        {
                            bFound = TRUE_D;
                            break;
                        }
                    }
                    if (!bFound)
                    {
                        return KEYHANDLE_ERROR_E;
                    }
                }
            }
        }
    }

    return KEYHANDLE_OK_E;
}

/**
* @brief Initialize default key mapping configuration.
* @remark Initialize default key mapping configuration, mapping logical keys to physical matrix keys.
*
* @param psConfig [in,out]: Configuration structure pointer.
* @return Operation status.
*/
KEYHANDLE_STATUS_E KeyHandle_eInitDefaultKeyMaps(KEYHANDLE_CONFIG_T* const psConfig)
{
    /* Parameter validity check */
    if (psConfig == NULL_D)
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Configure default key mapping (logical key to physical matrix key mapping) */
    psConfig->asSingleKeyMaps[0U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_LEFT_FUNC_E, 0U};    /* Left function key->Matrix position 0 */
    psConfig->asSingleKeyMaps[1U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_RIGHT_FUNC_E, 1U};   /* Right function key->Matrix position 1 */
    psConfig->asSingleKeyMaps[2U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_UP_E, 2U};           /* Up direction key->Matrix position 2 */
    psConfig->asSingleKeyMaps[3U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_DOWN_E, 3U};         /* Down direction key->Matrix position 3 */
    psConfig->asSingleKeyMaps[4U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_LEFT_E, 4U};         /* Left direction key->Matrix position 4 */
    psConfig->asSingleKeyMaps[5U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_RIGHT_E, 5U};        /* Right direction key->Matrix position 5 */
    psConfig->asSingleKeyMaps[6U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_START_E, 6U};        /* Start key->Matrix position 6 */
    psConfig->asSingleKeyMaps[7U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_STOP_E, 7U};         /* Stop key->Matrix position 7 */
    psConfig->asSingleKeyMaps[8U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_HELP_E, 8U};         /* Help key->Matrix position 8 */
    psConfig->asSingleKeyMaps[9U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_LOCAL_REMOTE_E, 9U}; /* Local/Remote key->Matrix position 9 */
    
    /* Initialize remaining mapping positions to invalid state */
    for (U8 i = 10U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        psConfig->asSingleKeyMaps[i] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_MAX_E, 0xFFU};
    }
    
    return KEYHANDLE_OK_E;
}

/**
* @brief Initialize default combination key mapping.
* @remark Initialize default combination key mapping, mapping combination keys to physical matrix keys.
*
* @param psConfig [in,out]: Configuration structure pointer.
* @return Operation status.
*/
KEYHANDLE_STATUS_E KeyHandle_eInitDefaultComboKeyMaps(KEYHANDLE_CONFIG_T* const psConfig)
{
    /* Parameter validity check */
    if (psConfig == NULL_D)
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Configure default combination key mapping (combination key to component keys mapping) */
    psConfig->asComboKeyMaps[0U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_LEFT_RIGHT_E, 2U, {KEYHANDLE_SINGLE_KEY_LEFT_E, KEYHANDLE_SINGLE_KEY_RIGHT_E}};     /* Left+Right direction keys */
    psConfig->asComboKeyMaps[1U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_LEFT_UP_E, 2U, {KEYHANDLE_SINGLE_KEY_LEFT_E, KEYHANDLE_SINGLE_KEY_UP_E}};           /* Left+Up direction keys */
    psConfig->asComboKeyMaps[2U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_LEFT_DOWN_E, 2U, {KEYHANDLE_SINGLE_KEY_LEFT_E, KEYHANDLE_SINGLE_KEY_DOWN_E}};       /* Left+Down direction keys */
    psConfig->asComboKeyMaps[3U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_RIGHT_UP_E, 2U, {KEYHANDLE_SINGLE_KEY_RIGHT_E, KEYHANDLE_SINGLE_KEY_UP_E}};         /* Right+Up direction keys */
    psConfig->asComboKeyMaps[4U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_RIGHT_DOWN_E, 2U, {KEYHANDLE_SINGLE_KEY_RIGHT_E, KEYHANDLE_SINGLE_KEY_DOWN_E}};     /* Right+Down direction keys */
    psConfig->asComboKeyMaps[5U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_LEFT_HELP_E, 2U, {KEYHANDLE_SINGLE_KEY_LEFT_E, KEYHANDLE_SINGLE_KEY_HELP_E}};       /* Left+Help keys */
    
    /* Initialize remaining combination key positions to invalid state */
    for (U8 i = 6U; i < KEYHANDLE_COMBO_MAX_E; i++)
    {
        psConfig->asComboKeyMaps[i] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_MAX_E, 0U, {0}};
    }
    
    return KEYHANDLE_OK_E;
}

/**
* @brief Key debounce processing.
* @remark Perform debounce processing on keys to eliminate key bounce.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Processing status.
*/
KEYHANDLE_STATUS_E KeyHandle_eDebounceKeys(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Get current time */
    U32 u32CurrentTime = KeyHandle_u32GetTickMs();

    /* Calculate state change bitmap */
    U16 u16Changed = psHandle->sState.u16CurrentMatrix ^ psHandle->sState.u16PreviousMatrix;
    
    /* Perform debounce processing for each key */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        /* Check if key state changed */
        if (u16Changed & (1U << i))
        {
            /* Key state changed, restart debounce timing */
            psHandle->sState.au32DebounceTime[i] = u32CurrentTime;
        }
        /* Check if debounce time has elapsed */
        else if ((u32CurrentTime - psHandle->sState.au32DebounceTime[i]) >= psHandle->sConfig.u32DebounceTimeMs)
        {
            /* Debounce time has elapsed, update debounced key state */
            if (psHandle->sState.u16CurrentMatrix & (1U << i))
            {
                /* Set key pressed state */
                psHandle->sState.u16DebouncedMatrix |= (1U << i);
            }
            else
            {
                /* Clear key pressed state */
                psHandle->sState.u16DebouncedMatrix &= ~(1U << i);
            }
        }
    }
    
    return KEYHANDLE_OK_E;
}

/**
* @brief Detect single key events.
* @remark Detect short press and long press events.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Processing status.
*/
KEYHANDLE_STATUS_E KeyHandle_eDetectKeyEvents(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Get current time */
    U32 u32CurrentTime = KeyHandle_u32GetTickMs();
    
    /* Currently pressed keys */
    U16 u16PressedKeys = psHandle->sState.u16DebouncedMatrix;
    /* Just released keys */
    U16 u16ReleasedKeys = psHandle->sState.u16PreviousMatrix & (~psHandle->sState.u16DebouncedMatrix);
    /* Just pressed keys */
    U16 u16NewPressed = psHandle->sState.u16DebouncedMatrix & (~psHandle->sState.u16PreviousMatrix);
    
    /* Process new key press state */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        if (u16NewPressed & (1U << i))
        {
            /* Record press time */
            psHandle->sState.au32PressTime[i] = u32CurrentTime;
            /* Clear processed flag */
            psHandle->sState.abProcessed[i] = FALSE_D;
        }
    }
    
    /* Process key release state */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        /* Check if key is released */
        if (u16ReleasedKeys & (1U << i))
        {
            /* Clear processed flag and long press trigger time */
            psHandle->sState.abProcessed[i] = FALSE_D;
            psHandle->sState.au32LastLongPressTime[i] = 0U;
        }
    }
    
    /* Detect and trigger short press events */
    if (psHandle->sConfig.bEnableShortPress)
    {
        for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
        {
            /* Check if key is released */
            if (u16ReleasedKeys & (1U << i))
            {
                /* Calculate press duration */
                U32 u32PressDuration = u32CurrentTime - psHandle->sState.au32PressTime[i];
                /* Check if short press conditions are met */
                if (u32PressDuration < psHandle->sConfig.u32LongPressTimeMs)
                {
                    /* Construct and trigger short press event */
                    KEYHANDLE_EVENT_T sEvent = {0U};
                    sEvent.eEventType = KEYHANDLE_EVENT_SINGLE_SHORT_PRESS_E;
                    sEvent.sKeyEventData.eSingleKey = KeyHandle_eMatrixToSingleKey(psHandle, i);
                    sEvent.sKeyEventData.u8MatrixKey = i;
                    sEvent.u32Timestamp = u32CurrentTime;
                    KeyHandle_vTriggerEvent(psHandle, &sEvent);
                }
            }
        }
    }
    
    /* Process long press event detection */
    if (psHandle->sConfig.bEnableLongPress)
    {
        for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
        {
            /* Check if key is pressed */
            if ((u16PressedKeys & (1U << i)) != 0U)
            {
                /* Calculate press duration */
                U32 u32PressDuration = u32CurrentTime - psHandle->sState.au32PressTime[i];
                
                /* Check if initial long press conditions are met */
                if ((u32PressDuration >= psHandle->sConfig.u32LongPressTimeMs) && (!psHandle->sState.abProcessed[i]))
                {
                    /* First long press trigger */
                    KEYHANDLE_EVENT_T sEvent = {0U};
                    sEvent.eEventType = KEYHANDLE_EVENT_SINGLE_LONG_PRESS_E;
                    sEvent.sKeyEventData.eSingleKey = KeyHandle_eMatrixToSingleKey(psHandle, i);
                    sEvent.sKeyEventData.u8MatrixKey = i;
                    sEvent.u32Timestamp = u32CurrentTime;
                    KeyHandle_vTriggerEvent(psHandle, &sEvent);
                    
                    /* Mark as processed and record first trigger time */
                    psHandle->sState.abProcessed[i] = TRUE_D;
                    psHandle->sState.au32LastLongPressTime[i] = u32CurrentTime;
                }
                /* Check for repeat long press triggers */
                else if (psHandle->sState.abProcessed[i] && 
                        ((u32CurrentTime - psHandle->sState.au32LastLongPressTime[i]) >= psHandle->sConfig.u32LongPressRepeatMs))
                {
                    /* Repeat long press trigger */
                    KEYHANDLE_EVENT_T sEvent = {0U};
                    sEvent.eEventType = KEYHANDLE_EVENT_SINGLE_LONG_PRESS_E;
                    sEvent.sKeyEventData.eSingleKey = KeyHandle_eMatrixToSingleKey(psHandle, i);
                    sEvent.sKeyEventData.u8MatrixKey = i;
                    sEvent.u32Timestamp = u32CurrentTime;
                    KeyHandle_vTriggerEvent(psHandle, &sEvent);
                    
                    /* Update last trigger time for next repeat */
                    psHandle->sState.au32LastLongPressTime[i] = u32CurrentTime;
                }
            }
        }
    }
    
    return KEYHANDLE_OK_E;
}

/**
* @brief Detect combination key events.
* @remark Detect currently pressed combination keys and trigger corresponding short press events.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Processing status.
*/
KEYHANDLE_STATUS_E KeyHandle_eDetectComboEvents(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity and function enable state check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized) || (!psHandle->sConfig.bEnableComboKeys))
    {
        return KEYHANDLE_ERROR_E;
    }
    
    /* Get current time */
    U32 u32CurrentTime = KeyHandle_u32GetTickMs();
    
    /* Get list of currently pressed single keys */
    KEYHANDLE_SINGLE_KEY_E aeCurrentKeys[KEYHANDLE_SINGLE_KEY_MAX_E];

    U8 u8CurrentKeyCount = 0U;
    
    /* Get list of currently pressed single keys */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        /* Check if key is pressed */
        if (psHandle->sState.u16DebouncedMatrix & (1U << i))
        {
            /* Get corresponding single key type */
            KEYHANDLE_SINGLE_KEY_E eSingleKey = KeyHandle_eMatrixToSingleKey(psHandle, i);

            if (eSingleKey != KEYHANDLE_SINGLE_KEY_MAX_E)
            {
                /* Add to current key list */
                aeCurrentKeys[u8CurrentKeyCount++] = eSingleKey;
            }
        }
    }
    
    /* Traverse all combination key mappings, check if there are matching combinations */
    for (U8 i = 0U; i < KEYHANDLE_COMBO_MAX_E; i++)
    {
        /* Check if combination key is enabled */
        if (psHandle->sConfig.asComboKeyMaps[i].u8KeyCount > 0U)
        {
            /* Check if combination key matches the current pressed keys */
            if (KeyHandle_bIsComboMatch(&psHandle->sConfig.asComboKeyMaps[i], aeCurrentKeys, u8CurrentKeyCount))
            {
                /* Check if it's a new combination key (avoid repeated triggering) */ 
                BOOL bNewCombo = (psHandle->sState.u8ActiveComboCount != u8CurrentKeyCount);

                if (!bNewCombo)
                {
                    /* Further check if combination content has changed */
                    for (U8 j = 0U; j < u8CurrentKeyCount; j++)
                    {
                        BOOL bFound = FALSE_D;

                        /* Check if key is in the current active combination key list */
                        for (U8 k = 0U; k < psHandle->sState.u8ActiveComboCount; k++)
                        {
                            if (aeCurrentKeys[j] == psHandle->sState.aeActiveCombo[k])
                            {
                                bFound = TRUE_D;
                                break;
                            }
                        }

                        /* Check if key is not in the current active combination key list */
                        if (!bFound)
                        {
                            bNewCombo = TRUE_D;
                            break;
                        }
                    }
                }

                /* If combination key is new */
                if (bNewCombo)
                {
                    /* Construct and trigger combination key short press event */
                    KEYHANDLE_EVENT_T sEvent = {0U};
                    sEvent.eEventType = KEYHANDLE_EVENT_COMBO_SHORT_PRESS_E;
                    sEvent.sComboEventData.eComboKey = psHandle->sConfig.asComboKeyMaps[i].eComboKey;
                    sEvent.sComboEventData.u8KeyCount = psHandle->sConfig.asComboKeyMaps[i].u8KeyCount;
                    memmove(sEvent.sComboEventData.aeKeys, 
                            psHandle->sConfig.asComboKeyMaps[i].aeKeys,
                            sizeof(KEYHANDLE_SINGLE_KEY_E) * psHandle->sConfig.asComboKeyMaps[i].u8KeyCount);
                    sEvent.u32Timestamp = u32CurrentTime;
                    KeyHandle_vTriggerEvent(psHandle, &sEvent);
                    
                    /* Update active combination key state */
                    psHandle->sState.u8ActiveComboCount = u8CurrentKeyCount;
                    memmove(psHandle->sState.aeActiveCombo, aeCurrentKeys, sizeof(KEYHANDLE_SINGLE_KEY_E) * u8CurrentKeyCount);

                    psHandle->sState.u32LastComboTime = u32CurrentTime;
                }

                /* Exit loop after finding matching combination key */
                break;
            }
        }
    }
    
    /* Clear expired combination key state */
    if ((u8CurrentKeyCount == 0U) || ((u32CurrentTime - psHandle->sState.u32LastComboTime) > psHandle->sConfig.u32ComboTimeoutMs))
    {
        psHandle->sState.u8ActiveComboCount = 0U;
    }
    
    return KEYHANDLE_OK_E;
}

/**
* @brief Convert matrix key index to single key type.
* @remark None.
*
* @param psHandle [in]: Key handling handle pointer.
* @param u8MatrixKey [in]: Matrix key index (0-15).
* @return Corresponding single key type.
*/
KEYHANDLE_SINGLE_KEY_E KeyHandle_eMatrixToSingleKey(const KEYHANDLE_HANDLE_T* const psHandle, U8 u8MatrixKey)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return KEYHANDLE_SINGLE_KEY_MAX_E;
    }
    
    /* Search for corresponding single key type in mapping table */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        if ((psHandle->sConfig.asSingleKeyMaps[i].eSingleKey != KEYHANDLE_SINGLE_KEY_MAX_E) && (psHandle->sConfig.asSingleKeyMaps[i].u8MatrixKey == u8MatrixKey))
        {
            return psHandle->sConfig.asSingleKeyMaps[i].eSingleKey;
        }
    }
    
    return KEYHANDLE_SINGLE_KEY_MAX_E;
}

/**
* @brief Check if combination key matches.
* @remark Check if combination key matches, return TRUE_D if all keys in the combination are in the current active key list.
*
* @param psCombo [in]: Combination key mapping pointer.
* @param aeActiveKeys [in]: Current active key list.
* @param u8KeyCount [in]: Number of currently active keys.
* @return Whether matches (TRUE_D-match, FALSE_D-no match).
*/
BOOL KeyHandle_bIsComboMatch(const KEYHANDLE_COMBO_KEY_MAP_T* const psCombo, const KEYHANDLE_SINGLE_KEY_E* const aeActiveKeys, U8 u8KeyCount)
{
    /* Parameter validity and key count check */
    if ((psCombo == NULL_D) || (aeActiveKeys == NULL_D) || (u8KeyCount != psCombo->u8KeyCount))
    {
        return FALSE_D;
    }
    
    /* Check if all keys in the combination are in the current active key list */
    for (U8 i = 0U; i < psCombo->u8KeyCount; i++)
    {
        BOOL bFound = FALSE_D;

        for (U8 j = 0U; j < u8KeyCount; j++)
        {
            /* Check if key is in the current active key list */
            if (psCombo->aeKeys[i] == aeActiveKeys[j])
            {
                bFound = TRUE_D;
                break;
            }
        }

        if (!bFound)
        {
            return FALSE_D;
        }
    }
    
    return TRUE_D;
}

/**
* @brief Trigger key event.
* @remark Call appropriate registered callback function based on event type and key.
*
* @param psHandle [in]: Key handling handle pointer.
* @param psEvent [in]: Event structure pointer.
* @return Nothing.
*/
void KeyHandle_vTriggerEvent(const KEYHANDLE_HANDLE_T* const psHandle, const KEYHANDLE_EVENT_T* const psEvent)
{
    /* Check parameter validity */
    if ((psHandle == NULL_D) || (psEvent == NULL_D))
    {
        return;
    }
    
    KeyHandle_EventCallback_t pfCallback = NULL_D;
    
    /* Get appropriate callback function based on event type */
    switch (psEvent->eEventType)
    {
        case KEYHANDLE_EVENT_SINGLE_SHORT_PRESS_E:
            /* Single key short press event */
            if (psEvent->sKeyEventData.eSingleKey < KEYHANDLE_SINGLE_KEY_MAX_E)
            {
                pfCallback = psHandle->sConfig.asSingleKeyShortPressCallbacks[psEvent->sKeyEventData.eSingleKey];
            }
            break;
            
        case KEYHANDLE_EVENT_SINGLE_LONG_PRESS_E:
            /* Single key long press event */
            if (psEvent->sKeyEventData.eSingleKey < KEYHANDLE_SINGLE_KEY_MAX_E)
            {
                pfCallback = psHandle->sConfig.asSingleKeyLongPressCallbacks[psEvent->sKeyEventData.eSingleKey];
            }
            break;
            
        case KEYHANDLE_EVENT_COMBO_SHORT_PRESS_E:
            /* Combination key short press event */
            if (psEvent->sComboEventData.eComboKey < KEYHANDLE_COMBO_MAX_E)
            {
                pfCallback = psHandle->sConfig.asComboKeyShortPressCallbacks[psEvent->sComboEventData.eComboKey];
            }
            break;
            
        default:
            /* Unknown event type */
            break;
    }
    
    /* Call callback function if it's registered */
    if (pfCallback != NULL_D)
    {
        pfCallback(psEvent);
    }
}

/**
* @brief Get system clock milliseconds.
* @remark Use FreeRTOS system clock function to get millisecond-level timestamp.
*
* @return Current system clock milliseconds.
*/
U32 KeyHandle_u32GetTickMs(void)
{
    U32 xTicks = (U32)xTaskGetTickCount();

    return xTicks * portTICK_PERIOD_MS;
}

//----------------------------------------------------------------------------
// State Machine Handler Functions Implementation:
//----------------------------------------------------------------------------

/**
* @brief State machine idle state handler.
* @remark Prepare for starting a new processing cycle.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Next state to transition to.
*/
KEYHANDLE_STATE_MACHINE_E KeyHandle_eStateIdle(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check - this should not happen if main process function checks properly */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        /* Cannot proceed without valid handle - stay in idle state */
        return KEYHANDLE_STATE_IDLE_E;
    }
    
    /* Save previous debounced state for edge detection */
    psHandle->sState.u16PreviousMatrix = psHandle->sState.u16DebouncedMatrix;
    
    /* Transition to scan state */
    return KEYHANDLE_STATE_SCAN_E;
}

/**
* @brief State machine scan state handler.
* @remark Scan key matrix and get current raw key state.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Next state to transition to.
*/
KEYHANDLE_STATE_MACHINE_E KeyHandle_eStateScan(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check - this should not happen if main process function checks properly */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        /* Cannot proceed without valid handle - reset state and return to idle */
        KeyHandle_vResetState(psHandle);
        return KEYHANDLE_STATE_IDLE_E;
    }
    
    /* Scan key matrix, get current raw key state */
    if (HalKey_eScanMatrix(&psHandle->sHalKey, &psHandle->sState.u16CurrentMatrix) != HALKEY_OK_E)
    {
        /* Hardware scan failed - reset current matrix and retry from idle state */
        psHandle->sState.u16CurrentMatrix = 0U;
        return KEYHANDLE_STATE_IDLE_E;
    }
    
    /* Transition to debounce state */
    return KEYHANDLE_STATE_DEBOUNCE_E;
}

/**
* @brief State machine debounce state handler.
* @remark Perform key debounce processing and decide next state based on key count.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Next state to transition to.
*/
KEYHANDLE_STATE_MACHINE_E KeyHandle_eStateDebounce(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check - this should not happen if main process function checks properly */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        /* Cannot proceed without valid handle - reset state and return to idle */
        KeyHandle_vResetState(psHandle);
        return KEYHANDLE_STATE_IDLE_E;
    }
    
    /* Perform key debounce processing */
    if (KeyHandle_eDebounceKeys(psHandle) != KEYHANDLE_OK_E)
    {
        /* Debounce processing failed - return to idle to retry */
        return KEYHANDLE_STATE_IDLE_E;
    }
    
    /* Update current key count */
    psHandle->sState.u8CurrentKeyCount = KeyHandle_u8GetCurrentKeyCount(psHandle);
    
    /* Decide next state based on current key count */
    if (psHandle->sState.u8CurrentKeyCount == 0U)
    {
        /* No keys pressed - clear combo mode and return to idle */
        psHandle->sState.bInComboMode = FALSE_D;
        return KEYHANDLE_STATE_IDLE_E;
    }
    else if (psHandle->sState.u8CurrentKeyCount == 1U)
    {
        /* Single key pressed */
        if (psHandle->sState.bInComboMode)
        {
            /* Coming from combo mode - ignore this single key and return to idle */
            return KEYHANDLE_STATE_IDLE_E;
        }
        else
        {
            /* Normal single key processing */
            return KEYHANDLE_STATE_DETECT_SINGLE_E;
        }
    }
    else
    {
        /* Multiple keys pressed - enter combo mode */
        if (!psHandle->sState.bInComboMode)
        {
            /* Entering combo mode - reset single key states */
            KeyHandle_vResetSingleKeyStates(psHandle);
            psHandle->sState.bInComboMode = TRUE_D;
        }
        return KEYHANDLE_STATE_DETECT_COMBO_E;
    }
}

/**
* @brief State machine single key detection state handler.
* @remark Detect single key events (press, release, short press, long press).
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Next state to transition to.
*/
KEYHANDLE_STATE_MACHINE_E KeyHandle_eStateDetectSingle(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check - this should not happen if main process function checks properly */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        /* Cannot proceed without valid handle - reset state and return to idle */
        KeyHandle_vResetState(psHandle);
        return KEYHANDLE_STATE_IDLE_E;
    }
    
    /* Detect single key events */
    if (KeyHandle_eDetectKeyEvents(psHandle) != KEYHANDLE_OK_E)
    {
        /* Event detection failed - continue processing, return to idle */
        return KEYHANDLE_STATE_IDLE_E;
    }
    
    /* Return to idle state to complete single key processing cycle */
    return KEYHANDLE_STATE_IDLE_E;
}

/**
* @brief State machine combination key detection state handler.
* @remark Detect combination key events.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Next state to transition to.
*/
KEYHANDLE_STATE_MACHINE_E KeyHandle_eStateDetectCombo(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check - this should not happen if main process function checks properly */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        /* Cannot proceed without valid handle - reset state and return to idle */
        KeyHandle_vResetState(psHandle);
        return KEYHANDLE_STATE_IDLE_E;
    }
    
    /* Detect combination key events */
    if (KeyHandle_eDetectComboEvents(psHandle) != KEYHANDLE_OK_E)
    {
        /* Event detection failed - continue processing, return to idle */
        return KEYHANDLE_STATE_IDLE_E;
    }
    
    /* Return to idle state to complete the cycle */
    return KEYHANDLE_STATE_IDLE_E;
}

/**
* @brief Reset state machine state to recover from error conditions.
* @remark Clear all state information and prepare for restart from idle state.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Nothing.
*/
void KeyHandle_vResetState(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if (psHandle == NULL_D)
    {
        /* Cannot proceed without valid handle */
        return;
    }
    
    /* Clear any pending state information to prevent corruption */
    psHandle->sState.u16CurrentMatrix = 0U;
    psHandle->sState.u16DebouncedMatrix = 0U;
    psHandle->sState.u8ActiveComboCount = 0U;
    psHandle->sState.bInComboMode = FALSE_D;
    psHandle->sState.u8CurrentKeyCount = 0U;
    
    /* Clear all timing arrays */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        psHandle->sState.au32PressTime[i] = 0U;
        psHandle->sState.au32DebounceTime[i] = 0U;
        psHandle->sState.au32LastLongPressTime[i] = 0U;
        psHandle->sState.abProcessed[i] = FALSE_D;
    }
    
    /* Set state machine to idle state */
    psHandle->sState.eStateMachine = KEYHANDLE_STATE_IDLE_E;
}

/**
* @brief Get current number of pressed keys.
* @remark Count the number of keys currently pressed based on debounced matrix.
*
* @param psHandle [in]: Key handling handle pointer.
* @return Number of currently pressed keys.
*/
U8 KeyHandle_u8GetCurrentKeyCount(const KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return 0U;
    }
    
    U8 u8KeyCount = 0U;
    
    /* Count pressed keys in debounced matrix */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        if (psHandle->sState.u16DebouncedMatrix & (1U << i))
        {
            /* Check if this matrix key is mapped to a valid single key */
            KEYHANDLE_SINGLE_KEY_E eSingleKey = KeyHandle_eMatrixToSingleKey(psHandle, i);
            if (eSingleKey != KEYHANDLE_SINGLE_KEY_MAX_E)
            {
                u8KeyCount++;
            }
        }
    }
    
    return u8KeyCount;
}

/**
* @brief Reset single key states to prevent interference with combo key processing.
* @remark Clear all single key related timing and processing states.
*
* @param psHandle [in,out]: Key handling handle pointer.
* @return Nothing.
*/
void KeyHandle_vResetSingleKeyStates(KEYHANDLE_HANDLE_T* const psHandle)
{
    /* Parameter validity check */
    if ((psHandle == NULL_D) || (!psHandle->bInitialized))
    {
        return;
    }
    
    /* Clear all single key timing states */
    for (U8 i = 0U; i < KEYHANDLE_SINGLE_KEY_MAX_E; i++)
    {
        psHandle->sState.au32PressTime[i] = 0U;
        psHandle->sState.au32LastLongPressTime[i] = 0U;
        psHandle->sState.abProcessed[i] = FALSE_D;
    }
}

//===========================================================================
// End of file.
//===========================================================================








